import React, { useState, useRef, useCallback, useEffect } from "react";
import ShortVideo from "./short-video";
import { useNavigate, useSearchParams } from "@remix-run/react";
import { useSwipeNavigation } from "~/hooks/useSwipeNavigation";
import EpisodeDrawer from "./video/EpisodeDrawer";
import { VideoData } from "~/types/videos";
import { EpisodeItem } from "~/types/index";
import LoadingSpinner from "~/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";

interface VideoFeedV2Props {
  videoData: VideoData;
  episodes?: EpisodeItem[];
}

// Number of videos to render on each side of the current video (e.g., 1 means current + 1 prev + 1 next = 3 total)
const RENDER_WINDOW_SIDE_COUNT = 1;
const BOTTOM_SWIPE_RESTRICT_HEIGHT_PX = 120; // Height of the bottom area where swipe is disabled

export default function VideoFeedV2({
  videoData,
  episodes = [],
}: VideoFeedV2Props) {
  const [searchParams] = useSearchParams();
  const activeEpisodeId = searchParams.get("episodeId");
  const { t } = useTranslation();

  const navigate = useNavigate();
  const containerRef = useRef<HTMLDivElement>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const {
    // Use the custom swipe navigation hook
    currentIndex,
    setCurrentIndex,
    isDragging,
    dragOffset,
    handleTouchStart: originalHandleTouchStart, // Renamed original handler
    handleTouchMove,
    handleTouchEnd,
  } = useSwipeNavigation({
    itemCount: episodes.length,
  });

  // Wrapper for handleTouchStart to restrict swipe area
  const handleTouchStartWrapper = (e: React.TouchEvent<HTMLDivElement>) => {
    const touchY = e.touches[0].clientY;
    // Calculate the top boundary of the restricted area (bottom of the screen minus restricted height)
    const restrictedAreaTopBoundary =
      window.innerHeight - BOTTOM_SWIPE_RESTRICT_HEIGHT_PX;

    if (touchY < restrictedAreaTopBoundary) {
      originalHandleTouchStart(e); // If touch is outside restricted area, proceed with swipe
    } else {
      // Touch is inside the restricted area, do nothing to prevent swipe
      // console.log("Swipe attempt in restricted bottom area.");
    }
  };

  const handleNextVideo = useCallback(() => {
    if (episodes.length === 0 || currentIndex >= episodes.length - 1) return;
    // Navigate to the next episode URL, setCurrentIndex will be handled by useEffect watching activeEpisodeId
    navigate(`?episodeId=${episodes[currentIndex + 1].id}`, {
      replace: true,
    });
  }, [episodes, currentIndex, navigate]);

  // Sync currentIndex with activeEpisodeId from URL
  useEffect(() => {
    if (activeEpisodeId && episodes.length > 0) {
      const episodeIndex = episodes.findIndex(
        (episode) => episode.id == activeEpisodeId
      );
      console.log("activeEpisodeId", activeEpisodeId);
      if (episodeIndex !== -1 && episodeIndex !== currentIndex) {
        console.log("Setting currentIndex to", episodeIndex);
        setCurrentIndex(episodeIndex);
      }
    } else if (!activeEpisodeId && episodes.length > 0 && currentIndex !== 0) {
      // If no episodeId in URL, default to the first one if not already there
      // This handles cases where the user lands on the page without a specific episode ID
      // or clears the episodeId from the URL.
      console.log("No episodeId in URL, defaulting to first episode");
      navigate(`?episodeId=${episodes[0].id}`, { replace: true });
      // setCurrentIndex will then be updated by the above block in the next render cycle
    }
  }, [activeEpisodeId, episodes]);

  // Update URL when currentIndex changes internally (e.g., by swipe)
  useEffect(() => {
    if (currentIndex >= 0 && episodes.length > 0 && episodes[currentIndex]) {
      console.log("Updating URL to episodeId", episodes[currentIndex].id);
      navigate(`?episodeId=${episodes[currentIndex].id}`, {
        replace: true,
      });
    }
  }, [currentIndex]);

  const handleClose = () => {
    navigate("/videos/1"); // Consider making this more dynamic or passed as a prop
  };

  const currentVideoDetails = videoData; // Assuming videoData is for the overall series
  const currentEpisodeForHandlers = episodes[currentIndex];

  const handleLike = (liked: boolean, currentLikes: number) => {
    if (!currentEpisodeForHandlers) return;
    console.log(
      `Episode ${currentEpisodeForHandlers.id} Liked: ${liked}, Count: ${currentLikes}`
    );
  };

  const handleSave = (saved: boolean) => {
    if (!currentEpisodeForHandlers) return;
    console.log(`Episode ${currentEpisodeForHandlers.id} Saved: ${saved}`);
  };

  const handleShare = () => {
    if (!currentEpisodeForHandlers) return;
    console.log(`Share episode ${currentEpisodeForHandlers.id}`);
  };

  const handleComment = () => {
    if (!currentEpisodeForHandlers) return;
    console.log(`Comment on episode ${currentEpisodeForHandlers.id}`);
  };

  const handleProfileClick = () => {
    if (!currentVideoDetails) return;
    console.log(`View profile for ${currentVideoDetails.username}`);
  };

  if (!videoData || episodes.length === 0) {
    return <LoadingSpinner fullScreen text={t("loadingContent")} />;
  }

  const videosToRender = [];
  const startIndex = Math.max(0, currentIndex - RENDER_WINDOW_SIDE_COUNT);
  const endIndex = Math.min(
    episodes.length - 1,
    currentIndex + RENDER_WINDOW_SIDE_COUNT
  );

  for (let i = 0; i < episodes.length; i++) {
    const episode = episodes[i];
    if (i >= startIndex && i <= endIndex) {
      videosToRender.push(
        <div
          key={episode.id} // Use stable episode ID as key
          className="w-full h-full flex-shrink-0"
        >
          <ShortVideo
            key={`shortvideo-${episode.id}`} // Stable key for ShortVideo
            muted={i !== currentIndex}
            isActive={i === currentIndex} // Pass isActive prop to indicate current video
            videoUrl={episode.playUrl || ""} // Provide fallback for potentially null URL
            subtitleUrl={episode.subtitle || ""} // Corrected prop name and added fallback
            posterUrl={currentVideoDetails.posterUrl} // Use main videoData for poster
            profileImageUrl={currentVideoDetails.profileImageUrl}
            username={currentVideoDetails.username}
            title={episode.title || currentVideoDetails.title} // Episode title or series title
            description={currentVideoDetails.description} // Fallback to series description
            tags={currentVideoDetails.tags}
            initialLikes={currentVideoDetails.initialLikes} // Fallback to series likes
            initialComments={currentVideoDetails.initialComments} // Fallback to series comments
            initialShares={currentVideoDetails.initialShares} // Fallback to series shares
            initialLiked={currentVideoDetails.initialLiked} // Fallback to series liked status
            initialSaved={currentVideoDetails.initialSaved} // Fallback to series saved status
            locked={episode.vip || currentVideoDetails.locked} // Episode specific lock, or series lock
            onClose={handleClose}
            onLike={handleLike}
            onSave={handleSave}
            onShare={handleShare}
            onComment={handleComment}
            onProfileClick={handleProfileClick}
            onVideoEnd={i === currentIndex ? handleNextVideo : undefined}
          />
        </div>
      );
    } else {
      // Render a lightweight placeholder
      videosToRender.push(
        <div
          key={`placeholder-${episode.id}`}
          className="w-full h-full flex-shrink-0"
        >
          {/* Placeholder: {episode.id} (index: {i}) */}
        </div>
      );
    }
  }

  return (
    <div
      ref={containerRef}
      className="fixed top-16 left-0 right-0 bottom-0 bg-black overflow-hidden"
      onTouchStart={handleTouchStartWrapper} // Use the wrapper here
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{ touchAction: "pan-y" }}
    >
      <div
        className={`h-full w-full relative ${
          !isDragging ? "transition-transform duration-300 ease-in-out" : ""
        } z-30`}
        style={{
          transform: `translateY(calc(-${
            currentIndex * 100
          }% + ${dragOffset}px))`,
        }}
      >
        {videosToRender}
      </div>
      <EpisodeDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        seriesTitle={currentVideoDetails.seriesTitle || "Unknown Series"}
        seasonNumber={currentVideoDetails.seasonNumber || 1}
        totalEpisodes={episodes.length}
        currentEpisode={currentIndex + 1} // Display 1-based index
        episodes={episodes}
        onEpisodeSelect={(episodeId) => {
          const selectedIndex = episodes.findIndex((ep) => ep.id === episodeId);
          if (selectedIndex !== -1) {
            // setCurrentIndex(selectedIndex); // This will be handled by URL change effect
            navigate(`?episodeId=${episodeId}`, { replace: true });
          }
          setDrawerOpen(false);
        }}
        videoData={currentVideoDetails}
      />
    </div>
  );
}
