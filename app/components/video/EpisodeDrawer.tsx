import React from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { ChevronUp, X, Loader2 } from "lucide-react";
import { EpisodeItem, VideoData } from "~/types/index";
import EpisodeGrid from "./EpisodeGrid";
import { useSearchParams } from "@remix-run/react";

interface EpisodeDrawerProps {
  videoData: VideoData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  seriesTitle: string;
  seasonNumber: number;
  totalEpisodes: number;
  currentEpisode: number;
  episodes: EpisodeItem[];
  loading?: boolean;
  onEpisodeSelect: (episodeId: string) => void;
}

/**
 * Episode selection drawer component that displays in a grid
 */
const EpisodeDrawer: React.FC<EpisodeDrawerProps> = ({
  open,
  onOpenChange,
  seriesTitle,
  seasonNumber,
  totalEpisodes,
  videoData,
  episodes,
  onEpisodeSelect,
  loading = false,
}) => {
  const [searchParams] = useSearchParams();
  const activeEpisodeId = searchParams.get("episodeId");
  const currentEpisode = episodes.find(
    (episode) => episode.id === activeEpisodeId
  );
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      {/* Visual container - full height */}
      <div className="absolute pointer-events-none bottom-0 left-0 right-0 px-3 pt-2.5 bg-neutral-900 text-white text-[10px] flex justify-between items-center z-10 h-12 border-t border-neutral-700/50">
        <span className="font-semibold truncate pr-2">
          {seriesTitle || "Unknown Series"}
        </span>
        <div className="flex items-center space-x-1 flex-shrink-0">
          <span>EP {currentEpisode?.title || "?"}</span>
          <ChevronUp size={14} />
        </div>

        {/* Smaller clickable trigger area */}
        <Dialog.Trigger asChild>
          <div className="absolute bottom-0 right-0 w-full h-8 cursor-pointer pointer-events-auto" />
        </Dialog.Trigger>
      </div>

      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 z-40 data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut" />
        <Dialog.Content className="fixed bottom-0 left-0 right-0 bg-main-bg rounded-t-xl max-h-[85vh] overflow-y-auto z-50 pb-safe transform transition-all duration-300 ease-out data-[state=open]:animate-slideUp data-[state=closed]:animate-slideDown">
          {/* Header */}
          <div className="flex justify-between items-start p-5 border-b border-neutral-700">
            <div>
              <h2 className="text-white text-xl font-bold">
                {currentEpisode?.title}
              </h2>
              <p className="text-neutral-400 text-sm">
                {/* {loading ? (
                  <span className="flex items-center">
                    <Loader2 size={14} className="animate-spin mr-2" />
                    Loading episodes...
                  </span>
                ) : (
                  `Updated to Episode ${totalEpisodes}`
                )} */}
              </p>
            </div>
            <Dialog.Close asChild>
              <button className="w-8 h-8 rounded-full bg-neutral-700 flex items-center justify-center text-white">
                <X size={16} />
              </button>
            </Dialog.Close>
          </div>

          {/* Loading state or episode grid */}
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 size={32} className="animate-spin text-white" />
            </div>
          ) : (
            <EpisodeGrid
              videoData={videoData}
              episodes={episodes}
              onEpisodeSelect={onEpisodeSelect}
            />
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default EpisodeDrawer;
